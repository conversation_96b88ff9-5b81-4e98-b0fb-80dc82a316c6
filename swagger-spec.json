{"openapi": "3.0.0", "paths": {"/api/v1/requests": {"post": {"operationId": "RequestsController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRequestDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}, "get": {"operationId": "RequestsController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationRequestResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}}, "/api/v1/requests/{id}": {"get": {"operationId": "RequestsController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}, "patch": {"operationId": "RequestsController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}, "delete": {"operationId": "RequestsController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Requests"]}}, "/api/v1/requests/{id}/accept": {"patch": {"operationId": "RequestsController_accept_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}}, "/api/v1/requests/{id}/reject": {"patch": {"operationId": "RequestsController_reject_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectRequestDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}}, "/api/v1/requests/{id}/hold": {"patch": {"operationId": "RequestsController_hold_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Request"}}}}}, "security": [{"bearer": []}], "tags": ["Requests"]}}, "/api/v1/offices": {"post": {"operationId": "OfficesController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOfficeDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}, "get": {"operationId": "OfficesController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationOfficeResponseDto"}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/owned": {"get": {"operationId": "OfficesController_getOwnedOffice_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}, "patch": {"operationId": "OfficesController_updateOwnedOffice_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOfficeDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}, "delete": {"operationId": "OfficesController_deleteOwnedOffice_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}}, "/api/v1/offices/nearby": {"get": {"operationId": "OfficesController_findNearbyOffices_v1", "parameters": [{"name": "latitude", "required": true, "in": "query", "schema": {"example": 36.778259, "type": "number"}}, {"name": "longitude", "required": true, "in": "query", "schema": {"example": 36.778259, "type": "number"}}, {"name": "radiusInKm", "required": true, "in": "query", "schema": {"example": 10, "type": "number"}}, {"name": "nearest", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "targetCurrency", "required": false, "in": "query", "schema": {"example": "USD", "type": "string"}}, {"name": "targetCurrencyRate", "required": false, "in": "query", "schema": {"example": 1, "type": "number"}}, {"name": "availableCurrencies", "required": false, "in": "query", "schema": {"example": ["USD", "EUR"], "type": "array", "items": {"type": "string"}}}, {"name": "baseCurrency", "required": false, "in": "query", "schema": {"example": "MAD", "type": "string"}}, {"name": "isOpen", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "isPopular", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "mostSearched", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "isFeatured", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "isVerified", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "isActive", "required": false, "in": "query", "schema": {"example": true, "type": "boolean"}}, {"name": "showOnlyOpenNow", "required": false, "in": "query", "description": "Show only offices that are currently open based on working hours", "schema": {"example": true, "type": "boolean"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/search-by-city": {"get": {"operationId": "OfficesController_searchCitiesWithNumberOfOfficeInCity_v1", "parameters": [{"name": "query", "required": true, "in": "query", "description": "Search query for city names", "schema": {"example": "Casa", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 50, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort results by", "schema": {"example": "officeCount", "type": "string", "enum": ["cityName", "officeCount", "newestOffice", "mostVerified", "mostFeatured"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"example": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}, {"name": "minOffices", "required": false, "in": "query", "description": "Minimum number of offices in city", "schema": {"minimum": 0, "example": 1, "type": "number"}}, {"name": "maxOffices", "required": false, "in": "query", "description": "Maximum number of offices in city", "schema": {"minimum": 1, "example": 100, "type": "number"}}, {"name": "onlyActiveOffices", "required": false, "in": "query", "description": "Include only cities with active offices", "schema": {"example": true, "type": "boolean"}}, {"name": "onlyVerifiedOffices", "required": false, "in": "query", "description": "Include only cities with verified offices", "schema": {"example": true, "type": "boolean"}}, {"name": "onlyFeaturedOffices", "required": false, "in": "query", "description": "Include only cities with featured offices", "schema": {"example": false, "type": "boolean"}}, {"name": "includeOfficeDetails", "required": false, "in": "query", "description": "Include detailed office information", "schema": {"example": true, "type": "boolean"}}, {"name": "availableCurrencies", "required": false, "in": "query", "description": "Filter by specific currency availability", "schema": {"example": ["USD", "EUR"], "type": "array", "items": {"type": "string"}}}, {"name": "includeStatistics", "required": false, "in": "query", "description": "Include statistics about offices in each city", "schema": {"example": true, "type": "boolean"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "Enhanced search results for cities with office information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCitiesWithOfficesResponse"}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/search-by-city-legacy": {"get": {"operationId": "OfficesController_searchCitiesWithNumberOfOfficeInCityLegacy_v1", "parameters": [{"name": "query", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "Legacy search endpoint for backward compatibility", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/city/{cityName}": {"get": {"operationId": "OfficesController_searchOfficesByCityName_v1", "parameters": [{"name": "cityName", "required": true, "in": "path", "description": "Name of the city to search offices in", "schema": {"example": "Casablanca", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number to retrieve.If you provide invalid value the default page number will applied\n        <p>\n             <b>Example: </b> 1\n          </p>\n        <p>\n             <b>Default Value: </b> 1\n          </p>\n        ", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of records per page.\n      <p>\n             <b>Example: </b> 20\n          </p>\n      <p>\n             <b>Default Value: </b> 20\n          </p>\n      <p>\n             <b>Max Value: </b> 100\n          </p>\n\n      If provided value is greater than max value, max value will be applied.\n      ", "schema": {"minimum": 1, "maximum": 50, "example": 10, "type": "number"}}, {"name": "availableCurrencies", "required": false, "in": "query", "description": "Filter by available currencies (comma-separated or array)", "schema": {"example": "USD,EUR,GBP", "type": "array", "items": {"type": "string"}}}, {"name": "trend", "required": false, "in": "query", "description": "Filter by trend type", "schema": {"example": "featured", "type": "string", "enum": ["featured", "verified", "newest"]}}, {"name": "showOnlyOpenNow", "required": false, "in": "query", "description": "Show only currently open offices", "schema": {"example": false, "type": "boolean"}}, {"name": "baseCurrency", "required": false, "in": "query", "description": "Base currency for conversion", "schema": {"example": "MAD", "type": "string"}}, {"name": "targetCurrency", "required": false, "in": "query", "description": "Target currency for conversion", "schema": {"example": "USD", "type": "string"}}, {"name": "targetCurrencyRate", "required": false, "in": "query", "description": "Target currency rate for conversion", "schema": {"minimum": 0, "example": 10.5, "type": "number"}}, {"name": "isActive", "required": false, "in": "query", "description": "Filter by active status", "schema": {"example": true, "type": "boolean"}}, {"name": "isVerified", "required": false, "in": "query", "description": "Filter by verified status", "schema": {"example": true, "type": "boolean"}}, {"name": "isFeatured", "required": false, "in": "query", "description": "Filter by featured status", "schema": {"example": false, "type": "boolean"}}, {"name": "filter.city", "required": false, "in": "query", "description": "Filter by city query param.\n          <p>\n             <b>Format: </b> filter.city={$not}:OPERATION:VALUE\n          </p>\n          <p>\n             <b>Example: </b> filter.city=$not:$like:<PERSON>&filter.city=like:<PERSON>\n          </p>\n          <h4>Available Operations</h4><ul><li>$eq</li></ul>", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.isActive", "required": false, "in": "query", "description": "Filter by isActive query param.\n          <p>\n             <b>Format: </b> filter.isActive={$not}:OPERATION:VALUE\n          </p>\n          <p>\n             <b>Example: </b> filter.isActive=$not:$like:<PERSON>&filter.isActive=like:<PERSON>\n          </p>\n          <h4>Available Operations</h4><ul><li>$eq</li></ul>", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.isVerified", "required": false, "in": "query", "description": "Filter by isVerified query param.\n          <p>\n             <b>Format: </b> filter.isVerified={$not}:OPERATION:VALUE\n          </p>\n          <p>\n             <b>Example: </b> filter.isVerified=$not:$like:<PERSON>&filter.isVerified=like:<PERSON>\n          </p>\n          <h4>Available Operations</h4><ul><li>$eq</li></ul>", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.isFeatured", "required": false, "in": "query", "description": "Filter by isFeatured query param.\n          <p>\n             <b>Format: </b> filter.isFeatured={$not}:OPERATION:VALUE\n          </p>\n          <p>\n             <b>Example: </b> filter.isFeatured=$not:$like:<PERSON>&filter.isFeatured=like:John\n          </p>\n          <h4>Available Operations</h4><ul><li>$eq</li></ul>", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "sortBy", "required": false, "in": "query", "description": "Parameter to sort by.\n      <p>To sort by multiple fields, just provide query param multiple types. The order in url defines an order of sorting</p>\n      <p>\n             <b>Format: </b> fieldName:DIRECTION\n          </p>\n      <p>\n             <b>Example: </b> sortBy=id:DESC&sortBy=createdAt:ASC\n          </p>\n      <p>\n             <b>Default Value: </b> createdAt:DESC\n          </p>\n      <h4>Available Fields</h4><ul><li>officeName</li>\n<li>registrationNumber</li>\n<li>currencyExchangeLicenseNumber</li>\n<li>address</li>\n<li>city</li>\n<li>country</li>\n<li>state</li>\n<li>location</li>\n<li>primaryPhoneNumber</li>\n<li>secondaryPhoneNumber</li>\n<li>thirdPhoneNumber</li>\n<li>whatsappNumber</li>\n<li>email</li>\n<li>slug</li>\n<li>isActive</li></ul>\n      ", "schema": {"type": "array", "items": {"type": "string", "enum": ["officeName:ASC", "officeName:DESC", "registrationNumber:ASC", "registrationNumber:DESC", "currencyExchangeLicenseNumber:ASC", "currencyExchangeLicenseNumber:DESC", "address:ASC", "address:DESC", "city:ASC", "city:DESC", "country:ASC", "country:DESC", "state:ASC", "state:DESC", "location:ASC", "location:DESC", "primaryPhoneNumber:ASC", "primaryPhoneNumber:DESC", "secondaryPhoneNumber:ASC", "secondaryPhoneNumber:DESC", "thirdPhoneNumber:ASC", "thirdPhoneNumber:DESC", "whatsappNumber:ASC", "whatsappNumber:DESC", "email:ASC", "email:DESC", "slug:ASC", "slug:DESC", "isActive:ASC", "isActive:DESC"]}}}, {"name": "search", "required": false, "in": "query", "description": "Search term to filter result values\n        <p>\n             <b>Example: </b> John\n          </p>\n        <p>\n             <b>Default Value: </b> No default value\n          </p>\n        ", "schema": {"type": "string"}}, {"name": "searchBy", "required": false, "in": "query", "description": "List of fields to search by term to filter result values\n        <p>\n             <b>Example: </b> officeName,registrationNumber,currencyExchangeLicenseNumber,address\n          </p>\n        <p>\n             <b>Default Value: </b> By default all fields mentioned below will be used to search by term\n          </p>\n        <h4>Available Fields</h4><ul><li>officeName</li>\n<li>registrationNumber</li>\n<li>currencyExchangeLicenseNumber</li>\n<li>address</li></ul>\n        ", "schema": {"type": "array", "items": {"type": "string"}}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/PaginatedDocumented"}, {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}, "meta": {"properties": {"select": {"type": "array", "items": {"type": "string"}}, "filter": {"type": "object", "properties": {"city": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "isActive": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "isVerified": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "isFeatured": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}}}}}}]}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/{id}": {"get": {"operationId": "OfficesController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}, "patch": {"operationId": "OfficesController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOfficeDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}, "delete": {"operationId": "OfficesController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Offices"]}}, "/api/v1/offices/slug/{slug}": {"get": {"operationId": "OfficesController_findBySlug_v1", "parameters": [{"name": "slug", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "tags": ["Offices"]}}, "/api/v1/offices/logo": {"post": {"operationId": "OfficesController_attachLogoToOffice_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachLogoDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}}, "/api/v1/offices/images": {"post": {"operationId": "OfficesController_attachImagesToOffice_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachImagesDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Office"}}}}}, "security": [{"bearer": []}], "tags": ["Offices"]}}, "/api/v1/cities": {"post": {"operationId": "CitiesController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCityDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/City"}}}}}, "tags": ["Cities"]}, "get": {"operationId": "CitiesController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationCityResponseDto"}}}}}, "tags": ["Cities"]}}, "/api/v1/cities/search": {"get": {"operationId": "CitiesController_searchByName_v1", "parameters": [{"name": "name", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationCityResponseDto"}}}}}, "tags": ["Cities"]}}, "/api/v1/cities/{id}": {"get": {"operationId": "CitiesController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/City"}}}}}, "tags": ["Cities"]}, "patch": {"operationId": "CitiesController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCityDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/City"}}}}}, "tags": ["Cities"]}, "delete": {"operationId": "CitiesController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "tags": ["Cities"]}}, "/api/v1/countries": {"post": {"operationId": "CountriesController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCountryDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Country"}}}}}, "security": [{"bearer": []}], "tags": ["Countries"]}, "get": {"operationId": "CountriesController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationCountryResponseDto"}}}}}, "tags": ["Countries"]}}, "/api/v1/countries/{id}": {"get": {"operationId": "CountriesController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Country"}}}}}, "tags": ["Countries"]}, "patch": {"operationId": "CountriesController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCountryDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Country"}}}}}, "security": [{"bearer": []}], "tags": ["Countries"]}, "delete": {"operationId": "CountriesController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Countries"]}}, "/api/v1/currencies": {"get": {"operationId": "CurrenciesController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationCurrencyResponseDto"}}}}}, "tags": ["Currencies"]}}, "/api/v1/currencies/{code}": {"get": {"operationId": "CurrenciesController_findByCode_v1", "parameters": [{"name": "code", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Currency"}}}}}, "tags": ["Currencies"]}}, "/api/v1/currencies/search/{query}": {"get": {"operationId": "CurrenciesController_searchByCode_v1", "parameters": [{"name": "query", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Currency"}}}}}, "tags": ["Currencies"]}}, "/api/v1/files/upload": {"post": {"operationId": "FilesLocalController_uploadFile_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"201": {"description": "File upload - returns single object for one file, array for multiple files", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["Files"]}}, "/api/v1/files/upload-multiple": {"post": {"operationId": "FilesLocalController_uploadFiles_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileResponseDto"}}}}}}, "security": [{"bearer": []}], "tags": ["Files"]}}, "/api/v1/faqs": {"post": {"operationId": "FaqsController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFaqDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}, "get": {"operationId": "FaqsController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationFaqResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}}, "/api/v1/faqs/me": {"get": {"operationId": "FaqsController_getMyOfficeFaqs_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationFaqResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}, "post": {"operationId": "FaqsController_createMyOfficeFaq_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMyFaqDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}}, "/api/v1/faqs/me/bulk": {"post": {"description": "Allows office owners to create multiple FAQs in a single request. All FAQs will be associated with the authenticated user's office.", "operationId": "FaqsController_createMyOfficeFaqs_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMyFaqsDto"}}}}, "responses": {"201": {"description": "Successfully created FAQs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Faq"}}}}}, "400": {"description": "Invalid input data or validation errors"}, "401": {"description": "User not authenticated or office not found"}}, "security": [{"bearer": []}], "summary": "Create multiple FAQs for authenticated user office", "tags": ["Faqs"]}}, "/api/v1/faqs/me/{id}": {"patch": {"operationId": "FaqsController_updateMyOfficeFaq_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFaqDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}, "delete": {"operationId": "FaqsController_removeMyOfficeFaq_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Faqs"]}}, "/api/v1/faqs/{id}": {"get": {"operationId": "FaqsController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}, "patch": {"operationId": "FaqsController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFaqDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}, "security": [{"bearer": []}], "tags": ["Faqs"]}, "delete": {"operationId": "FaqsController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Faqs"]}}, "/api/v1/landing-pages/city-ranking": {"get": {"operationId": "LandingPagesController_getCityRankingForExchangeRates_v1", "parameters": [{"name": "baseCurrencyCode", "required": true, "in": "query", "schema": {"example": "USD", "type": "string"}}, {"name": "targetCurrencyCode", "required": true, "in": "query", "schema": {"example": "EUR", "type": "string"}}, {"name": "amount", "required": true, "in": "query", "schema": {"example": 1, "type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "tags": ["Landingpages"]}}, "/api/v1/office-rates": {"post": {"operationId": "OfficeRatesController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOfficeRateDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeRate"}}}}}, "security": [{"bearer": []}], "tags": ["Officerates"]}, "get": {"operationId": "OfficeRatesController_getOfficeRatesByOfficeId_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Officerates"]}}, "/api/v1/office-rates/{id}": {"get": {"operationId": "OfficeRatesController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeRate"}}}}}, "tags": ["Officerates"]}, "patch": {"operationId": "OfficeRatesController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOfficeRateDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeRate"}}}}}, "security": [{"bearer": []}], "tags": ["Officerates"]}, "delete": {"operationId": "OfficeRatesController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Officerates"]}}, "/api/v1/office-rates/bulk-update": {"post": {"description": "Updates or creates currency rates for multiple offices identified by their slugs. If a rate exists for a currency, it will be updated. If not, a new rate will be created.", "operationId": "OfficeRatesController_bulkUpdateOfficeRates_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateOfficeRatesDto"}}}}, "responses": {"201": {"description": "Bulk update completed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "results": {"type": "object", "properties": {"updated": {"type": "number"}, "created": {"type": "number"}, "errors": {"type": "number"}}}, "details": {"type": "array", "items": {"type": "object", "properties": {"officeSlug": {"type": "string"}, "currency": {"type": "string"}, "action": {"type": "string", "enum": ["updated", "created", "error"]}, "message": {"type": "string"}}}}}}}}}}, "summary": "Bulk update office rates for multiple offices", "tags": ["Officerates"]}}, "/api/v1/rate-histories": {"post": {"operationId": "RateHistoriesController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRateHistoryDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RateHistory"}}}}}, "security": [{"bearer": []}], "tags": ["Ratehistories"]}, "get": {"operationId": "RateHistoriesController_getRateHistoriesByOfficeId_v1", "parameters": [{"name": "timePeriod", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "Get rate histories filtered by time period", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RateHistory"}}}}}}, "security": [{"bearer": []}], "tags": ["Ratehistories"]}}, "/api/v1/rate-histories/{id}": {"get": {"operationId": "RateHistoriesController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RateHistory"}}}}}, "security": [{"bearer": []}], "tags": ["Ratehistories"]}, "patch": {"operationId": "RateHistoriesController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRateHistoryDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RateHistory"}}}}}, "security": [{"bearer": []}], "tags": ["Ratehistories"]}, "delete": {"operationId": "RateHistoriesController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Ratehistories"]}}, "/api/v1/working-hours": {"post": {"operationId": "WorkingHoursController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkingHourDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkingHour"}}}}}, "security": [{"bearer": []}], "tags": ["Workinghours"]}, "get": {"operationId": "WorkingHoursController_getMyOfficeWorkingHours_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "Working hours for the authenticated user office", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkingHour"}}}}}, "404": {"description": "Office not found for user"}}, "security": [{"bearer": []}], "summary": "Get working hours for authenticated user office", "tags": ["Workinghours"]}, "patch": {"operationId": "WorkingHoursController_updateMyOfficeWorkingHours_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWorkingHoursDto"}}}}, "responses": {"200": {"description": "Working hours updated successfully"}, "400": {"description": "Invalid data provided"}, "404": {"description": "Office not found for user"}}, "security": [{"bearer": []}], "summary": "Update working hours for authenticated user office", "tags": ["Workinghours"]}}, "/api/v1/working-hours/{id}": {"get": {"operationId": "WorkingHoursController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkingHour"}}}}}, "security": [{"bearer": []}], "tags": ["Workinghours"]}}, "/api/v1/alerts": {"post": {"operationId": "AlertsController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAlertDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Alert"}}}}}, "tags": ["<PERSON><PERSON><PERSON>"]}, "get": {"operationId": "AlertsController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationAlertResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON><PERSON>"]}}, "/api/v1/alerts/{id}": {"get": {"operationId": "AlertsController_findById_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Alert"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON><PERSON>"]}, "patch": {"operationId": "AlertsController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAlertDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Alert"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON><PERSON>"]}, "delete": {"operationId": "AlertsController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON><PERSON>"]}}, "/api/v1/users": {"post": {"operationId": "UsersController_create_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearer": []}], "tags": ["Users"]}, "get": {"operationId": "UsersController_findAll_v1", "parameters": [{"name": "page", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "number"}}, {"name": "filters", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "sort", "required": false, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfinityPaginationUserResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["Users"]}}, "/api/v1/users/{id}": {"get": {"operationId": "UsersController_findOne_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearer": []}], "tags": ["Users"]}, "patch": {"operationId": "UsersController_update_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearer": []}], "tags": ["Users"]}, "delete": {"operationId": "UsersController_remove_v1", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Users"]}}, "/api/v1/analytics/dashboard": {"get": {"operationId": "AnalyticsController_getDashboard_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Analytics"]}}, "/api/v1/analytics/track/profile-view/{officeId}": {"post": {"operationId": "AnalyticsController_trackProfileView_v1", "parameters": [{"name": "officeId", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"201": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Analytics"]}}, "/api/v1/analytics/track/phone-call/{officeId}": {"post": {"operationId": "AnalyticsController_trackPhoneCall_v1", "parameters": [{"name": "officeId", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackPhoneCallDto"}}}}, "responses": {"201": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Analytics"]}}, "/api/v1/analytics/track/gps-click/{officeId}": {"post": {"operationId": "AnalyticsController_trackGpsClick_v1", "parameters": [{"name": "officeId", "required": true, "in": "path", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"201": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Analytics"]}}, "/api/v1/admins/analytics/stats": {"get": {"operationId": "AdminsController_getAnalyticsStats_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/activity/stats": {"get": {"operationId": "AdminsController_getActivityStatistics_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/activity/list": {"get": {"operationId": "AdminsController_getOfficeActivityList_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "cityId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "countryId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "isActiveOnly", "required": true, "in": "query", "schema": {"type": "boolean"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "offset", "required": true, "in": "query", "schema": {"type": "number"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/dashboard/stats": {"get": {"operationId": "AdminsController_getDashboardStats_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/dashboard/table": {"get": {"operationId": "AdminsController_getDashboardTable_v1", "parameters": [{"name": "period", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "cityId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "countryId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "isActiveOnly", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/office-engagement": {"get": {"operationId": "AdminsController_getOfficeEngagement_v1", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Time period for engagement data (Last 7 days, Last 30 days, Last 90 days)", "schema": {"enum": ["7days", "30days", "90days"], "type": "string"}}, {"name": "cityId", "required": false, "in": "query", "description": "Single city ID for backward compatibility", "schema": {"example": "city1", "type": "string"}}, {"name": "cityIds", "required": false, "in": "query", "description": "Comma-separated list of city IDs to filter by (supports multiple city selection)", "schema": {"example": "city1,city2,city3", "type": "string"}}, {"name": "countryId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "isActiveOnly", "required": false, "in": "query", "description": "Filter to show only active offices", "schema": {"example": true, "type": "boolean"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "search", "required": false, "in": "query", "description": "Search by office name or city name", "schema": {"example": "Casablanca", "type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/filters/cities": {"get": {"operationId": "AdminsController_getCitiesForFilter_v1", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search cities by name", "schema": {"type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/admins/about-offices": {"get": {"operationId": "AdminsController_getAboutOffices_v1", "parameters": [{"name": "countryId", "required": false, "in": "query", "description": "Filter by country ID", "schema": {"example": "country-uuid", "type": "string"}}, {"name": "cityIds", "required": false, "in": "query", "description": "Filter by multiple city IDs (comma-separated)", "schema": {"example": "city-uuid-1,city-uuid-2", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by office status", "schema": {"enum": ["REQUESTED", "ON_HOLD", "ACCEPTED", "REJECTED"], "type": "string"}}, {"name": "duration", "required": false, "in": "query", "description": "Filter by duration on platform", "schema": {"enum": ["ALL_TIME", "LAST_7_DAYS", "LAST_1_MONTH", "LAST_6_MONTHS"], "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"example": 10, "type": "number"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for office name or city", "schema": {"example": "Exchange Office", "type": "string"}}, {"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "Get comprehensive office information with filtering capabilities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AboutOfficesResponse"}}}}}, "security": [{"bearer": []}], "tags": ["Admins"]}}, "/api/v1/notification-preferences/me": {"get": {"description": "Get the current user notification preferences", "operationId": "NotificationPreferencesController_getMyPreferences_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationPreference"}}}}}, "security": [{"bearer": []}], "summary": "Get my notification preferences", "tags": ["Notification Preferences"]}, "patch": {"description": "Update the current user notification preferences", "operationId": "NotificationPreferencesController_updateMyPreferences_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationPreferenceDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationPreference"}}}}}, "security": [{"bearer": []}], "summary": "Update my notification preferences", "tags": ["Notification Preferences"]}}, "/api/v1/rate-update-notifications/trigger-check": {"post": {"description": "Manually trigger the rate update reminder check for testing purposes", "operationId": "RateUpdateNotificationsController_triggerRateUpdateCheck_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "summary": "Manually trigger rate update check (Admin only)", "tags": ["Rate Update Notifications"]}}, "/api/v1/auth/email/login": {"post": {"operationId": "AuthController_login_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthEmailLoginDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/email/register": {"post": {"operationId": "AuthController_register_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthRegisterLoginDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/email/confirm": {"post": {"operationId": "AuthController_confirmEmail_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthConfirmEmailDto"}}}}, "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/email/confirm/new": {"post": {"operationId": "AuthController_confirmNewEmail_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthConfirmEmailDto"}}}}, "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/forgot/password": {"post": {"operationId": "AuthController_forgotPassword_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthForgotPasswordDto"}}}}, "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/reset/password": {"post": {"operationId": "AuthController_resetPassword_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResetPasswordDto"}}}}, "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/change/password": {"post": {"operationId": "AuthController_changePassword_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthChangePasswordDto"}}}}, "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/me": {"get": {"operationId": "AuthController_me_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}, "patch": {"operationId": "AuthController_update_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUpdateDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}, "delete": {"operationId": "AuthController_delete_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/refresh": {"post": {"operationId": "AuthController_refresh_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshResponseDto"}}}}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/logout": {"post": {"operationId": "AuthController_logout_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"204": {"description": ""}}, "security": [{"bearer": []}], "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/facebook/login": {"post": {"operationId": "AuthFacebookController_login_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthFacebookLoginDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/google/login": {"post": {"operationId": "AuthGoogleController_login_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthGoogleLoginDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/api/v1/auth/apple/login": {"post": {"operationId": "AuthAppleController_login_v1", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthAppleLoginDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/": {"get": {"operationId": "HomeController_appInfo", "parameters": [{"in": "header", "required": false, "name": "x-custom-lang", "schema": {"example": "en"}}], "responses": {"200": {"description": ""}}, "tags": ["Home"]}}}, "info": {"title": "ExchangGo24 API", "description": " API docs", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateRequestDto": {"type": "object", "properties": {}}, "FileType": {"type": "object", "properties": {"id": {"type": "string", "example": "cbcfa8b8-3a25-4adb-a9c6-e325f0d0f3ae"}, "path": {"type": "string", "example": "https://example.com/path/to/file.jpg"}}, "required": ["id", "path"]}, "Role": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string", "example": "admin"}}, "required": ["id", "name"]}, "Status": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string", "example": "active"}}, "required": ["id", "name"]}, "User": {"type": "object", "properties": {"id": {"type": "number"}, "email": {"type": "string", "example": "<EMAIL>"}, "provider": {"type": "string", "example": "email"}, "socialId": {"type": "string", "example": "**********"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "photo": {"$ref": "#/components/schemas/FileType"}, "role": {"$ref": "#/components/schemas/Role"}, "status": {"$ref": "#/components/schemas/Status"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "deletedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "email", "provider", "socialId", "firstName", "lastName", "photo", "role", "status", "createdAt", "updatedAt", "deletedAt"]}, "WorkingHour": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the working hour record", "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"}, "dayOfWeek": {"type": "string", "description": "Day of the week", "example": "Monday", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}, "isActive": {"type": "boolean", "description": "Indicates if the day is active for working hours", "example": true}, "fromTime": {"type": "string", "description": "Start time of working hours", "example": "09:00", "nullable": true}, "toTime": {"type": "string", "description": "End time of working hours", "example": "17:00", "nullable": true}, "hasBreak": {"type": "boolean", "description": "Indicates if there is a break during this day", "example": false}, "breakFromTime": {"type": "string", "description": "Start time of break", "example": "12:00", "nullable": true}, "breakToTime": {"type": "string", "description": "End time of break", "example": "13:00", "nullable": true}, "officeId": {"type": "string", "description": "ID of the associated office", "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation timestamp", "example": "2023-01-01T12:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp", "example": "2023-01-02T12:00:00Z"}}, "required": ["id", "dayOfWeek", "isActive", "fromTime", "toTime", "hasBreak", "breakFromTime", "breakToTime", "officeId", "createdAt", "updatedAt"]}, "Office": {"type": "object", "properties": {"id": {"type": "string"}, "owner": {"$ref": "#/components/schemas/User"}, "officeName": {"type": "string"}, "location": {"type": "object"}, "registrationNumber": {"type": "string"}, "currencyExchangeLicenseNumber": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "object"}, "state": {"type": "object"}, "country": {"type": "object"}, "primaryPhoneNumber": {"type": "string"}, "secondaryPhoneNumber": {"type": "object"}, "thirdPhoneNumber": {"type": "object"}, "whatsappNumber": {"type": "object"}, "logo": {"type": "object"}, "slug": {"type": "object"}, "isActive": {"type": "boolean"}, "isVerified": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "deletedAt": {"type": "object"}, "distanceInKm": {"type": "number"}, "rates": {"type": "array", "items": {"type": "string"}}, "equivalentValue": {"type": "number"}, "images": {"type": "object"}, "workingHours": {"type": "array", "items": {"type": "string"}}, "email": {"type": "object"}, "todayWorkingHours": {"$ref": "#/components/schemas/WorkingHour"}}, "required": ["id", "owner", "officeName", "location", "registrationNumber", "currencyExchangeLicenseNumber", "address", "city", "state", "country", "primaryPhoneNumber", "secondaryPhoneNumber", "thirdPhoneNumber", "whatsappNumber", "logo", "slug", "isActive", "isVerified", "isFeatured", "createdAt", "updatedAt", "deletedAt", "distanceInKm", "rates", "equivalentValue", "images", "workingHours", "email", "todayWorkingHours"]}, "Request": {"type": "object", "properties": {"id": {"type": "string"}, "office": {"$ref": "#/components/schemas/Office"}, "status": {"type": "string"}, "rejectReason": {"type": "string"}, "additionalMessage": {"type": "string"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "office", "status", "rejectReason", "additionalMessage", "createdAt", "updatedAt"]}, "InfinityPaginationRequestResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Request"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateRequestDto": {"type": "object", "properties": {}}, "RejectRequestDto": {"type": "object", "properties": {"rejectReason": {"type": "string", "example": "Reject Reason"}, "additionalMessage": {"type": "string", "example": "Additional Message"}}, "required": ["rejectReason"]}, "FileDto": {"type": "object", "properties": {"id": {"type": "string"}}, "required": ["id"]}, "CreateOfficeDto": {"type": "object", "properties": {"officeName": {"type": "string", "example": "Office Name"}, "registrationNumber": {"type": "string", "example": "AAB-**********"}, "currencyExchangeLicenseNumber": {"type": "string", "example": "AAB-**********"}, "address": {"type": "string", "example": "123 Main St, Rabat, Morocco"}, "city": {"type": "string", "example": "Rabat"}, "state": {"type": "string", "example": "Ra<PERSON><PERSON><PERSON><PERSON>"}, "primaryPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "secondaryPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "thirdPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "whatsappNumber": {"type": "string", "example": "+212 661 23 45 67"}, "logo": {"example": "logo.png", "allOf": [{"$ref": "#/components/schemas/FileDto"}]}, "location": {"type": "object", "example": "{ type: \"Point\", coordinates: [longitude, latitude] }"}}, "required": ["officeName", "registrationNumber", "currencyExchangeLicenseNumber", "address", "city", "state", "primaryPhoneNumber", "location"]}, "InfinityPaginationOfficeResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateOfficeDto": {"type": "object", "properties": {"officeName": {"type": "string", "example": "Office Name"}, "registrationNumber": {"type": "string", "example": "AAB-**********"}, "currencyExchangeLicenseNumber": {"type": "string", "example": "AAB-**********"}, "address": {"type": "string", "example": "123 Main St, Rabat, Morocco"}, "city": {"type": "string", "example": "Rabat"}, "state": {"type": "string", "example": "Ra<PERSON><PERSON><PERSON><PERSON>"}, "primaryPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "secondaryPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "thirdPhoneNumber": {"type": "string", "example": "+212 661 23 45 67"}, "whatsappNumber": {"type": "string", "example": "+212 661 23 45 67"}, "logo": {"example": "logo.png", "allOf": [{"$ref": "#/components/schemas/FileDto"}]}, "location": {"type": "object", "example": "{ type: \"Point\", coordinates: [longitude, latitude] }"}}}, "Country": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "unicode": {"type": "string"}, "emoji": {"type": "string"}, "alpha2": {"type": "string"}, "dialCode": {"type": "string"}, "region": {"type": "string"}, "capital": {"type": "string"}, "alpha3": {"type": "string"}}, "required": ["id", "name", "unicode", "emoji", "alpha2", "dialCode", "region", "capital", "alpha3"]}, "City": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "country": {"$ref": "#/components/schemas/Country"}, "office": {"$ref": "#/components/schemas/Office"}, "offices": {"description": "Array of offices in this city", "type": "array", "items": {"$ref": "#/components/schemas/Office"}}, "numberOfOffices": {"type": "number", "description": "Number of offices in this city"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "name", "country", "office", "offices", "numberOfOffices", "createdAt", "updatedAt"]}, "OfficeStatistics": {"type": "object", "properties": {"totalOffices": {"type": "number", "description": "Total number of offices in the city", "example": 15}, "activeOffices": {"type": "number", "description": "Number of active offices", "example": 12}, "verifiedOffices": {"type": "number", "description": "Number of verified offices", "example": 8}, "featuredOffices": {"type": "number", "description": "Number of featured offices", "example": 3}, "averageRating": {"type": "number", "description": "Average rating of offices (if available)", "example": 4.2}, "newestOfficeDate": {"format": "date-time", "type": "string", "description": "Most recent office creation date", "example": "2024-01-15T10:30:00Z"}, "availableCurrencies": {"description": "Available currencies in this city", "example": ["USD", "EUR", "GBP"], "type": "array", "items": {"type": "string"}}, "currentlyOpenOffices": {"type": "number", "description": "Number of offices currently open", "example": 5}}, "required": ["totalOffices", "activeOffices", "verifiedOffices", "featuredOffices", "availableCurrencies", "currentlyOpenOffices"]}, "SimplifiedOffice": {"type": "object", "properties": {"id": {"type": "string", "description": "Office ID", "example": "123e4567-e89b-12d3-a456-426614174000"}, "officeName": {"type": "string", "description": "Office name", "example": "Exchange Plus Casablanca"}, "address": {"type": "string", "description": "Office address", "example": "123 Hassan II Boulevard, Casablanca"}, "primaryPhoneNumber": {"type": "string", "description": "Primary phone number", "example": "+212522123456"}, "slug": {"type": "string", "description": "Office slug for URL", "example": "exchange-plus-casablanca"}, "isActive": {"type": "boolean", "description": "Whether the office is active", "example": true}, "isVerified": {"type": "boolean", "description": "Whether the office is verified", "example": true}, "isFeatured": {"type": "boolean", "description": "Whether the office is featured", "example": false}, "logoUrl": {"type": "string", "description": "Office logo URL", "example": "https://example.com/logo.jpg"}, "isCurrentlyOpen": {"type": "boolean", "description": "Whether the office is currently open", "example": true}, "todayWorkingHours": {"type": "string", "description": "Today's working hours", "example": "09:00 - 17:00"}}, "required": ["id", "officeName", "address", "primaryPhoneNumber", "slug", "isActive", "isVerified", "isFeatured"]}, "CityWithOfficesResponse": {"type": "object", "properties": {"city": {"description": "City information", "allOf": [{"$ref": "#/components/schemas/City"}]}, "statistics": {"description": "Office statistics for this city", "allOf": [{"$ref": "#/components/schemas/OfficeStatistics"}]}, "offices": {"description": "List of offices in the city (if requested)", "type": "array", "items": {"$ref": "#/components/schemas/SimplifiedOffice"}}, "detailedOffices": {"description": "Detailed office information (if requested)", "type": "array", "items": {"$ref": "#/components/schemas/Office"}}}, "required": ["city", "statistics"]}, "SearchMetadata": {"type": "object", "properties": {"page": {"type": "number", "description": "Current page number", "example": 1}, "limit": {"type": "number", "description": "Number of items per page", "example": 10}, "totalCities": {"type": "number", "description": "Total number of cities found", "example": 25}, "totalPages": {"type": "number", "description": "Total number of pages", "example": 3}, "hasNextPage": {"type": "boolean", "description": "Whether there are more pages", "example": true}, "hasPreviousPage": {"type": "boolean", "description": "Whether there are previous pages", "example": false}}, "required": ["page", "limit", "totalCities", "totalPages", "hasNextPage", "hasPreviousPage"]}, "SearchSummary": {"type": "object", "properties": {"totalOfficesFound": {"type": "number", "description": "Total offices across all cities", "example": 150}, "totalActiveOffices": {"type": "number", "description": "Total active offices", "example": 120}, "totalVerifiedOffices": {"type": "number", "description": "Total verified offices", "example": 80}, "searchQuery": {"type": "string", "description": "Search query used", "example": "Casa"}, "executionTimeMs": {"type": "number", "description": "Search execution time in milliseconds", "example": 45}}, "required": ["totalOfficesFound", "totalActiveOffices", "totalVerifiedOffices", "searchQuery", "executionTimeMs"]}, "SearchCitiesWithOfficesResponse": {"type": "object", "properties": {"data": {"description": "List of cities with their office information", "type": "array", "items": {"$ref": "#/components/schemas/CityWithOfficesResponse"}}, "meta": {"description": "Pagination metadata", "allOf": [{"$ref": "#/components/schemas/SearchMetadata"}]}, "summary": {"description": "Search summary statistics", "allOf": [{"$ref": "#/components/schemas/SearchSummary"}]}}, "required": ["data", "meta", "summary"]}, "PaginatedMetaDocumented": {"type": "object", "properties": {"itemsPerPage": {"type": "number", "title": "Number of items per page"}, "totalItems": {"type": "number", "title": "Total number of items"}, "currentPage": {"type": "number", "title": "Current requested page"}, "totalPages": {"type": "number", "title": "Total number of pages"}, "sortBy": {"type": "array", "title": "Sorting by columns", "items": {"type": "array", "items": {"oneOf": [{"type": "string"}, {"type": "string", "enum": ["ASC", "DESC"]}]}}}, "searchBy": {"title": "Search by fields", "type": "array", "items": {"type": "string"}}, "search": {"type": "string", "title": "Search term"}, "select": {"title": "List of selected fields", "type": "array", "items": {"type": "string"}}, "filter": {"type": "object", "title": "Filters that applied to the query", "additionalProperties": false}}, "required": ["itemsPerPage", "totalItems", "currentPage", "totalPages", "filter"]}, "PaginatedLinksDocumented": {"type": "object", "properties": {"first": {"type": "string", "title": "Link to first page"}, "previous": {"type": "string", "title": "Link to previous page"}, "current": {"type": "string", "title": "Link to current page"}, "next": {"type": "string", "title": "Link to next page"}, "last": {"type": "string", "title": "Link to last page"}}}, "PaginatedDocumented": {"type": "object", "properties": {"data": {"title": "Array of entities", "additionalProperties": false, "type": "array", "items": {"type": "object"}}, "meta": {"title": "Pagination Metadata", "allOf": [{"$ref": "#/components/schemas/PaginatedMetaDocumented"}]}, "links": {"title": "Links to pages", "allOf": [{"$ref": "#/components/schemas/PaginatedLinksDocumented"}]}}, "required": ["data", "meta", "links"]}, "FileUploadResponseDto": {"type": "object", "properties": {"file": {"$ref": "#/components/schemas/FileDto"}}, "required": ["file"]}, "AttachLogoDto": {"type": "object", "properties": {"logo": {"description": "File upload response from the upload endpoint", "example": {"file": {"path": "/api/v1/files/393939152967bd44f6e63.png", "id": "087a9c1b-7bc7-4f4c-bbdb-8883f41b7c17"}}, "allOf": [{"$ref": "#/components/schemas/FileUploadResponseDto"}]}}, "required": ["logo"]}, "AttachImagesDto": {"type": "object", "properties": {"images": {"description": "Array of file upload responses from the upload endpoint", "example": [{"file": {"path": "/api/v1/files/393939152967bd44f6e63.png", "id": "087a9c1b-7bc7-4f4c-bbdb-8883f41b7c17"}}], "type": "array", "items": {"$ref": "#/components/schemas/FileUploadResponseDto"}}}, "required": ["images"]}, "CountryDto": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "unicode": {"type": "string"}, "emoji": {"type": "string"}, "alpha2": {"type": "string"}, "dialCode": {"type": "string"}, "region": {"type": "string"}, "capital": {"type": "string"}, "alpha3": {"type": "string"}}, "required": ["id", "name", "unicode", "emoji", "alpha2", "dialCode", "region", "capital", "alpha3"]}, "CreateCityDto": {"type": "object", "properties": {"name": {"type": "string", "example": "Rabat"}, "country": {"example": "MA", "allOf": [{"$ref": "#/components/schemas/CountryDto"}]}}, "required": ["name", "country"]}, "InfinityPaginationCityResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateCityDto": {"type": "object", "properties": {"name": {"type": "string", "example": "Rabat"}, "country": {"example": "MA", "allOf": [{"$ref": "#/components/schemas/CountryDto"}]}}}, "CreateCountryDto": {"type": "object", "properties": {"name": {"type": "string"}, "unicode": {"type": "string"}, "emoji": {"type": "string"}, "alpha2": {"type": "string"}, "dialCode": {"type": "string"}, "alpha3": {"type": "string"}, "region": {"type": "string"}, "capital": {"type": "string"}}, "required": ["name", "unicode", "emoji", "alpha2", "dialCode", "alpha3", "region", "capital"]}, "InfinityPaginationCountryResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateCountryDto": {"type": "object", "properties": {"name": {"type": "string"}, "unicode": {"type": "string"}, "emoji": {"type": "string"}, "alpha2": {"type": "string"}, "dialCode": {"type": "string"}, "alpha3": {"type": "string"}, "region": {"type": "string"}, "capital": {"type": "string"}}}, "Currency": {"type": "object", "properties": {"id": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}, "namePlural": {"type": "string"}, "symbol": {"type": "string"}, "symbolNative": {"type": "string"}, "decimalDigits": {"type": "number"}, "rounding": {"type": "number"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "flag": {"type": "string"}}, "required": ["id", "code", "name", "namePlural", "symbol", "symbolNative", "decimalDigits", "rounding", "createdAt", "updatedAt", "flag"]}, "InfinityPaginationCurrencyResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Currency"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "FileResponseDto": {"type": "object", "properties": {"file": {"$ref": "#/components/schemas/FileType"}}, "required": ["file"]}, "CreateFaqDto": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "officeId": {"type": "string"}}, "required": ["question", "answer", "officeId"]}, "Faq": {"type": "object", "properties": {"id": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}, "isActive": {"type": "boolean"}, "office": {"$ref": "#/components/schemas/Office"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "question", "answer", "isActive", "office", "createdAt", "updatedAt"]}, "InfinityPaginationFaqResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Faq"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "CreateMyFaqDto": {"type": "object", "properties": {"question": {"type": "string", "description": "The question for the FAQ", "example": "What are your exchange rates?"}, "answer": {"type": "string", "description": "The answer for the FAQ", "example": "Our exchange rates are updated daily and are competitive with market rates."}}, "required": ["question", "answer"]}, "CreateMyFaqsDto": {"type": "object", "properties": {"faqs": {"description": "Array of FAQs to create", "example": [{"question": "What are your exchange rates?", "answer": "Our exchange rates are updated daily and are competitive with market rates."}, {"question": "What are your working hours?", "answer": "We are open Monday to Friday from 9 AM to 6 PM."}], "type": "array", "items": {"$ref": "#/components/schemas/CreateMyFaqDto"}}}, "required": ["faqs"]}, "UpdateFaqDto": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "officeId": {"type": "string"}}}, "CreateOfficeRateDto": {"type": "object", "properties": {"targetCurrency": {"type": "string", "example": "USD"}, "buyRate": {"type": "number", "example": 10}, "sellRate": {"type": "number", "example": 10}, "isActive": {"type": "boolean", "example": true}}, "required": ["targetCurrency", "buyRate", "sellRate", "isActive"]}, "OfficeRate": {"type": "object", "properties": {"id": {"type": "string"}, "office": {"$ref": "#/components/schemas/Office"}, "buyRate": {"type": "number"}, "sellRate": {"type": "number"}, "baseCurrency": {"$ref": "#/components/schemas/Currency"}, "targetCurrency": {"$ref": "#/components/schemas/Currency"}, "isActive": {"type": "boolean"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}, "deletedAt": {"type": "object"}, "buyRateEquivalent": {"type": "number"}, "sellRateEquivalent": {"type": "number"}, "equivalentValue": {"type": "number"}}, "required": ["id", "office", "buyRate", "sellRate", "baseCurrency", "targetCurrency", "isActive", "createdAt", "updatedAt", "deletedAt", "buyRateEquivalent", "sellRateEquivalent", "equivalentValue"]}, "UpdateOfficeRateDto": {"type": "object", "properties": {}}, "CurrencyRateDto": {"type": "object", "properties": {"currency": {"type": "string", "example": "EUR", "description": "Currency code (3 letters)"}, "buy": {"type": "number", "example": 10.3, "description": "Buy rate for the currency"}, "sell": {"type": "number", "example": 10.65, "description": "Sell rate for the currency"}}, "required": ["currency", "buy", "sell"]}, "BulkUpdateOfficeRatesDto": {"type": "object", "properties": {"rates": {"description": "Array of currency rates to update", "example": [{"currency": "EUR", "buy": 10.3, "sell": 10.65}, {"currency": "USD", "buy": 8.85, "sell": 9.25}], "type": "array", "items": {"$ref": "#/components/schemas/CurrencyRateDto"}}, "officeSlugs": {"description": "Array of office slugs to update", "example": ["change-goulmima-exchange-7j7", "el-jadida-cash", "sara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "change-boulevard-roudani-change-maarif-casablanca"], "type": "array", "items": {"type": "string"}}}, "required": ["rates", "officeSlugs"]}, "CreateRateHistoryDto": {"type": "object", "properties": {}}, "RateHistory": {"type": "object", "properties": {"id": {"type": "string"}, "office": {"$ref": "#/components/schemas/Office"}, "targetCurrency": {"$ref": "#/components/schemas/Currency"}, "baseCurrency": {"$ref": "#/components/schemas/Currency"}, "oldBuyRate": {"type": "number"}, "oldSellRate": {"type": "number"}, "newBuyRate": {"type": "number"}, "newSellRate": {"type": "number"}, "isActive": {"type": "boolean"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "office", "targetCurrency", "baseCurrency", "oldBuyRate", "oldSellRate", "newBuyRate", "newSellRate", "isActive", "createdAt", "updatedAt"]}, "UpdateRateHistoryDto": {"type": "object", "properties": {}}, "CreateWorkingHourDto": {"type": "object", "properties": {"dayOfWeek": {"type": "string", "description": "Day of the week", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "example": "Monday"}, "isActive": {"type": "boolean", "description": "Indicates if the day is active for working hours", "example": true}, "fromTime": {"type": "string", "description": "Start time of working hours (HH:MM format)", "example": "09:00"}, "toTime": {"type": "string", "description": "End time of working hours (HH:MM format)", "example": "17:00"}, "hasBreak": {"type": "boolean", "description": "Indicates if there is a break during this day", "example": false, "default": false}, "breakFromTime": {"type": "string", "description": "Start time of break (HH:MM format)", "example": "12:00"}, "breakToTime": {"type": "string", "description": "End time of break (HH:MM format)", "example": "13:00"}, "officeId": {"type": "string", "description": "ID of the associated office", "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6"}}, "required": ["dayOfWeek", "isActive", "hasBreak", "officeId"]}, "UpdateWorkingHoursDto": {"type": "object", "properties": {}}, "CreateAlertDto": {"type": "object", "properties": {"triggerType": {"type": "string", "example": "CITY", "enum": ["CITY", "OFFICE"], "description": "Type of trigger for the alert. Must be either CITY or OFFICE"}, "whatsAppNumber": {"type": "string", "example": "+************", "description": "WhatsApp number in international format (e.g., +************)"}, "cities": {"example": ["685e6c55-95cb-46a9-877c-05bf28<PERSON>eaff"], "description": "Array of city UUIDs for city-based alerts (required when triggerType is CITY)", "type": "array", "items": {"type": "string"}}, "offices": {"example": ["48bc5c92-8cea-4dcf-8b47-3a6c9f33e6ce", "12345678-1234-1234-1234-**********12"], "description": "Array of office UUIDs for office-based alerts (required when triggerType is OFFICE)", "type": "array", "items": {"type": "string"}}, "currency": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "user": {"type": "string", "example": "a1b2c3d4-e5f6-7890-abcd-ef**********", "description": "User UUID (optional, for authenticated users)"}, "baseCurrencyAmount": {"type": "number", "example": "Base Currency Amount"}, "targetCurrencyAmount": {"type": "number", "example": "Target Currency Amount"}, "targetCurrency": {"type": "string", "example": "Target Currency"}}, "required": ["triggerType", "whatsAppNumber", "cities", "offices", "currency", "user", "baseCurrencyAmount", "targetCurrencyAmount", "targetCurrency"]}, "Alert": {"type": "object", "properties": {"id": {"type": "string"}, "triggerType": {"type": "string"}, "whatsAppNumber": {"type": "string"}, "cities": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}, "offices": {"type": "array", "items": {"$ref": "#/components/schemas/Office"}}, "currency": {"$ref": "#/components/schemas/Currency"}, "user": {"$ref": "#/components/schemas/User"}, "baseCurrencyAmount": {"type": "number"}, "targetCurrencyAmount": {"type": "number"}, "targetCurrency": {"$ref": "#/components/schemas/Currency"}, "isActive": {"type": "boolean"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "triggerType", "whatsAppNumber", "cities", "offices", "currency", "user", "baseCurrencyAmount", "targetCurrencyAmount", "targetCurrency", "isActive", "createdAt", "updatedAt"]}, "InfinityPaginationAlertResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateAlertDto": {"type": "object", "properties": {"triggerType": {"type": "string", "example": "CITY", "enum": ["CITY", "OFFICE"], "description": "Type of trigger for the alert. Must be either CITY or OFFICE"}, "whatsAppNumber": {"type": "string", "example": "+************", "description": "WhatsApp number in international format (e.g., +************)"}, "cities": {"example": ["685e6c55-95cb-46a9-877c-05bf28<PERSON>eaff"], "description": "Array of city UUIDs for city-based alerts (required when triggerType is CITY)", "type": "array", "items": {"type": "string"}}, "offices": {"example": ["48bc5c92-8cea-4dcf-8b47-3a6c9f33e6ce", "12345678-1234-1234-1234-**********12"], "description": "Array of office UUIDs for office-based alerts (required when triggerType is OFFICE)", "type": "array", "items": {"type": "string"}}, "currency": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "user": {"type": "string", "example": "a1b2c3d4-e5f6-7890-abcd-ef**********", "description": "User UUID (optional, for authenticated users)"}, "baseCurrencyAmount": {"type": "number", "example": "Base Currency Amount"}, "targetCurrencyAmount": {"type": "number", "example": "Target Currency Amount"}, "targetCurrency": {"type": "string", "example": "Target Currency"}}}, "RoleDto": {"type": "object", "properties": {"id": {"type": "object"}}, "required": ["id"]}, "StatusDto": {"type": "object", "properties": {"id": {"type": "object"}}, "required": ["id"]}, "CreateUserDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "photo": {"$ref": "#/components/schemas/FileDto"}, "role": {"$ref": "#/components/schemas/RoleDto"}, "status": {"$ref": "#/components/schemas/StatusDto"}}, "required": ["email", "password", "firstName", "lastName"]}, "InfinityPaginationUserResponseDto": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "hasNextPage": {"type": "boolean", "example": true}}, "required": ["data", "hasNextPage"]}, "UpdateUserDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "photo": {"$ref": "#/components/schemas/FileDto"}, "role": {"$ref": "#/components/schemas/RoleDto"}, "status": {"$ref": "#/components/schemas/StatusDto"}}}, "TrackPhoneCallDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "example": "Phone Number"}, "phoneType": {"type": "string", "example": "Phone Type"}}, "required": ["phoneNumber", "phoneType"]}, "AboutOfficeData": {"type": "object", "properties": {"officeName": {"type": "string", "description": "Office name", "example": "Exchange Office Downtown"}, "city": {"type": "string", "description": "City name", "example": "Casablanca"}, "country": {"type": "string", "description": "Country name", "example": "Morocco"}, "registrationDate": {"format": "date-time", "type": "string", "description": "Office registration date", "example": "2024-01-15T10:30:00Z"}, "status": {"type": "string", "description": "Office status from requests or ACCEPTED if not available", "example": "ACCEPTED", "enum": ["REQUESTED", "ON_HOLD", "ACCEPTED", "REJECTED"]}, "duration": {"type": "number", "description": "Duration on platform in days", "example": 45}}, "required": ["officeName", "city", "country", "registrationDate", "status", "duration"]}, "AboutOfficesResponse": {"type": "object", "properties": {"data": {"description": "Array of office data", "type": "array", "items": {"$ref": "#/components/schemas/AboutOfficeData"}}, "totalOffices": {"type": "number", "description": "Total number of offices matching filters", "example": 150}, "filteredCount": {"type": "number", "description": "Number of offices returned in current page", "example": 10}, "appliedFilters": {"type": "object", "description": "Applied filters summary", "example": {"countryId": "country-uuid", "cityIds": ["city-uuid-1", "city-uuid-2"], "status": "ACCEPTED", "duration": "LAST_1_MONTH", "search": "Exchange"}}, "pagination": {"type": "object", "description": "Pagination information", "example": {"currentPage": 1, "pageSize": 10, "totalPages": 15, "totalItems": 150, "hasNextPage": true, "hasPreviousPage": false}}}, "required": ["data", "totalOffices", "filteredCount", "appliedFilters", "pagination"]}, "NotificationPreference": {"type": "object", "properties": {"id": {"type": "string"}, "rateUpdateReminderWhatsApp": {"type": "boolean", "description": "Enable WhatsApp notifications for rate update reminders"}, "rateUpdateReminderEmail": {"type": "boolean", "description": "Enable email notifications for rate update reminders"}, "createdAt": {"format": "date-time", "type": "string"}, "updatedAt": {"format": "date-time", "type": "string"}}, "required": ["id", "rateUpdateReminderWhatsApp", "rateUpdateReminderEmail", "createdAt", "updatedAt"]}, "UpdateNotificationPreferenceDto": {"type": "object", "properties": {"rateUpdateReminderWhatsApp": {"type": "boolean", "description": "Enable WhatsApp notifications for rate update reminders", "example": true}, "rateUpdateReminderEmail": {"type": "boolean", "description": "Enable email notifications for rate update reminders", "example": true}}}, "AuthEmailLoginDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string"}}, "required": ["email", "password"]}, "LoginResponseDto": {"type": "object", "properties": {"token": {"type": "string"}, "refreshToken": {"type": "string"}, "tokenExpires": {"type": "number"}, "user": {"$ref": "#/components/schemas/User"}}, "required": ["token", "refreshToken", "tokenExpires", "user"]}, "AuthRegisterLoginDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}}, "required": ["email", "password", "firstName", "lastName"]}, "AuthConfirmEmailDto": {"type": "object", "properties": {"hash": {"type": "string"}}, "required": ["hash"]}, "AuthForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}}, "required": ["email"]}, "AuthResetPasswordDto": {"type": "object", "properties": {"password": {"type": "string"}, "hash": {"type": "string"}}, "required": ["password", "hash"]}, "AuthChangePasswordDto": {"type": "object", "properties": {"oldPassword": {"type": "string", "example": "currentPassword123", "description": "Current password of the user"}, "newPassword": {"type": "string", "example": "newPassword123", "description": "New password for the user"}, "confirmPassword": {"type": "string", "example": "newPassword123", "description": "Confirmation of the new password"}}, "required": ["oldPassword", "newPassword", "confirmPassword"]}, "RefreshResponseDto": {"type": "object", "properties": {"token": {"type": "string"}, "refreshToken": {"type": "string"}, "tokenExpires": {"type": "number"}}, "required": ["token", "refreshToken", "tokenExpires"]}, "AuthUpdateDto": {"type": "object", "properties": {"photo": {"$ref": "#/components/schemas/FileDto"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string"}, "oldPassword": {"type": "string"}}}, "AuthFacebookLoginDto": {"type": "object", "properties": {"accessToken": {"type": "string", "example": "abc"}}, "required": ["accessToken"]}, "AuthGoogleLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "example": "abc"}}, "required": ["idToken"]}, "AuthAppleLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "example": "abc"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["idToken"]}}}}